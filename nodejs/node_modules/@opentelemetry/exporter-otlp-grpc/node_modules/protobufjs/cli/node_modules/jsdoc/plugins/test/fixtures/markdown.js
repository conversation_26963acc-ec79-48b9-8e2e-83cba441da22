'use strict';

/**
 * @see [Nowhere](http://nowhere.com)
 */
function foo() {}

/**
 * @see AnObject#myProperty
 */
function bar() {}

/**
 * <AUTHOR> <PERSON>](http://www.folklore.org/StoryView.py?story=Mister_Macintosh.txt)
 * @classdesc My class.
 * @description My class.
 * @exception {Error} Some error.
 * @param {string} myParam - My parameter.
 * @property {string} value - Value of myParam.
 * @return {MyClass} Class instance.
 * @see [Example Inc.](http://example.com)
 * @summary My class.
 */
function MyClass(myParam) {
    this.value = myParam;
}

/**
 * "See" {@link chat."#channel"."say-\"hello\""}.
 */
function MyOtherClass() {}
