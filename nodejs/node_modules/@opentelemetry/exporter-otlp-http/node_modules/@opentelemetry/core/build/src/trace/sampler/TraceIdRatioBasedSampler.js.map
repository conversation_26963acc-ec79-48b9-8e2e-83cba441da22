{"version": 3, "file": "TraceIdRatioBasedSampler.js", "sourceRoot": "", "sources": ["../../../../src/trace/sampler/TraceIdRatioBasedSampler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAK4B;AAE5B,2FAA2F;AAC3F,MAAa,wBAAwB;IAGnC,YAA6B,SAAiB,CAAC;QAAlB,WAAM,GAAN,MAAM,CAAY;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,YAAY,CAAC,OAAgB,EAAE,OAAe;QAC5C,OAAO;YACL,QAAQ,EACN,oBAAc,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW;gBACrE,CAAC,CAAC,sBAAgB,CAAC,kBAAkB;gBACrC,CAAC,CAAC,sBAAgB,CAAC,UAAU;SAClC,CAAC;IACJ,CAAC;IAED,QAAQ;QACN,OAAO,qBAAqB,IAAI,CAAC,MAAM,GAAG,CAAC;IAC7C,CAAC;IAEO,UAAU,CAAC,KAAa;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACxD,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjD,CAAC;IAEO,WAAW,CAAC,OAAe;QACjC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAClB,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACvD,YAAY,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5C;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AAnCD,4DAmCC"}