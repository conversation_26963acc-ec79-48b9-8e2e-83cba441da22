{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/baggage/utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAiC,8BAA8B,EAAE,MAAM,oBAAoB,CAAC;AACnG,OAAO,EACL,uBAAuB,EACvB,4BAA4B,EAC5B,0BAA0B,EAC1B,wBAAwB,GACzB,MAAM,aAAa,CAAC;AAIrB,MAAM,UAAU,iBAAiB,CAAC,QAAkB;IAClD,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAC,MAAc,EAAE,OAAe;QACrD,IAAM,KAAK,GAAG,KAAG,MAAM,IAAG,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,IACjE,OAAS,CAAC;QACf,OAAO,KAAK,CAAC,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IAClE,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,OAAgB;IAC1C,OAAO,OAAO;SACX,aAAa,EAAE;SACf,GAAG,CACF,UAAC,EAAY;YAAX,GAAG,QAAA,EAAE,KAAK,QAAA;QACV,OAAG,kBAAkB,CAAC,GAAG,CAAC,SAAI,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAG;IAA/D,CAA+D,CAClE,CAAC;AACN,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,KAAa;IAC7C,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAC7D,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO;IACnC,IAAM,WAAW,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;IACvC,IAAI,CAAC,WAAW;QAAE,OAAO;IACzB,IAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO;IACjC,IAAM,GAAG,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAClD,IAAM,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,IAAI,QAAQ,CAAC;IACb,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,QAAQ,GAAG,8BAA8B,CACvC,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAC9C,CAAC;KACH;IACD,OAAO,EAAE,GAAG,KAAA,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,CAAC;AAClC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CAAC,KAAc;IACpD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAC/D,OAAO,KAAK;SACT,KAAK,CAAC,uBAAuB,CAAC;SAC9B,GAAG,CAAC,UAAA,KAAK;QACR,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC;SACD,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAjD,CAAiD,CAAC;SACpE,MAAM,CAAyB,UAAC,OAAO,EAAE,OAAO;QAC/C,oEAAoE;QACpE,OAAO,CAAC,OAAQ,CAAC,GAAG,CAAC,GAAG,OAAQ,CAAC,KAAK,CAAC;QACvC,OAAO,OAAO,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC"}