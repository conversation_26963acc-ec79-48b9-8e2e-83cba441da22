{
  "name": "nodejs-tracing-sample",
  "version": "1.0.0",
  "description": "Node.js OpenTelemetry tracing sample application",
  "main": "app.js",
  "scripts": {
    "start": "node app.js",
    // "dev": "nodemon app.js",
    "serviceA": "dotenv -e .env.service-a -- npm start",
    "serviceB": "dotenv -e .env.service-b -- npm start",
    "serviceC": "dotenv -e .env.service-c -- npm start",
    "start:all": "concurrently \"npm run serviceA\" \"npm run serviceB\" \"npm run serviceC\""
  },
  "dependencies": {
    "@opentelemetry/api": "^1.7.0",
    "@opentelemetry/auto-instrumentations-node": "^0.40.0",
    "@opentelemetry/exporter-otlp-grpc": "^0.26.0",
    "@opentelemetry/exporter-trace-otlp-proto": "^0.47.0",
    "@opentelemetry/resources": "^1.20.0",
    "@opentelemetry/sdk-node": "^0.45.0",
    "@opentelemetry/semantic-conventions": "^1.18.0",
    "axios": "^1.6.0",
    "express": "^4.18.2",
    "winston": "^3.11.0"
  },
  "devDependencies": {
    "concurrently": "^9.2.1",
    "dotenv-cli": "^10.0.0",
    "nodemon": "^3.0.1"
  },
  "keywords": [
    "opentelemetry",
    "tracing",
    "nodejs",
    "express",
    "observability"
  ],
  "author": "Your Name",
  "license": "MIT"
}
