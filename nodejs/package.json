{"name": "nodejs-tracing-sample", "version": "1.0.0", "description": "Node.js OpenTelemetry tracing sample application", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "winston": "^3.11.0", "@opentelemetry/sdk-node": "^0.45.0", "@opentelemetry/auto-instrumentations-node": "^0.40.0", "@opentelemetry/exporter-otlp-grpc": "^0.45.0", "@opentelemetry/resources": "^1.18.0", "@opentelemetry/semantic-conventions": "^1.18.0", "@opentelemetry/api": "^1.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["opentelemetry", "tracing", "nodejs", "express", "observability"], "author": "Your Name", "license": "MIT"}