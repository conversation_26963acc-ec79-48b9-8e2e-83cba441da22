{"ServiceDetails": [{"ServiceName": "com.amazonaws.vpce.eu-west-1.vpce-svc-0e4b81f6f7b0d2cc5", "ServiceId": "vpce-svc-0e4b81f6f7b0d2cc5", "ServiceType": [{"ServiceType": "Interface"}], "AvailabilityZones": ["eu-west-1a", "eu-west-1c"], "Owner": "625240141681", "BaseEndpointDnsNames": ["vpce-svc-0e4b81f6f7b0d2cc5.eu-west-1.vpce.amazonaws.com"], "PrivateDnsName": "ingress.private.eu1.coralogix.com", "PrivateDnsNames": [{"PrivateDnsName": "ingress.private.eu1.coralogix.com"}], "VpcEndpointPolicySupported": false, "AcceptanceRequired": false, "ManagesVpcEndpoints": false, "Tags": [], "PrivateDnsNameVerificationState": "verified", "SupportedIpAddressTypes": ["ipv4"]}], "ServiceNames": ["com.amazonaws.vpce.eu-west-1.vpce-svc-0e4b81f6f7b0d2cc5"]}