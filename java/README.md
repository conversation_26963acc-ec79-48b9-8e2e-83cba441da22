# Java OpenTelemetry Tracing Sample

This sample application demonstrates distributed tracing with OpenTelemetry in Java using Spring Boot.

## Architecture

The application consists of three services:
- **Service A (Port 8080)**: Main API that handles product requests
- **Service B (Port 8081)**: Category service that manages product categories
- **Service C (Port 8082)**: Tags service that provides product tags

## Features

- ✅ OpenTelemetry auto-instrumentation for Spring Boot and HTTP requests
- ✅ Distributed tracing across multiple services
- ✅ JSON structured logging with trace ID and span ID correlation
- ✅ Variable latencies (100-2000ms)
- ✅ Multiple HTTP status codes (200, 400, 500) with 10% error rate each
- ✅ OTLP export to collector
- ✅ Ready for Coralogix APM integration

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Start all services:**
   ```bash
   docker-compose up --build
   ```

2. **Test the application:**
   ```bash
   # Generate traces with different outcomes
   curl http://localhost:8080/api/products
   curl http://localhost:8081/api/categories
   curl http://localhost:8082/api/tags
   ```

3. **View traces in Jaeger UI:**
   Open http://localhost:16686

### Option 2: Local Development

1. **Prerequisites:**
   - Java 17+
   - Maven 3.6+

2. **Build the application:**
   ```bash
   mvn clean package
   ```

3. **Start OTEL Collector:**
   ```bash
   docker run -p 4317:4317 -p 4318:4318 \
     -v $(pwd)/otel-collector-config.yaml:/etc/otel-collector-config.yaml \
     otel/opentelemetry-collector-contrib:latest \
     --config=/etc/otel-collector-config.yaml
   ```

4. **Start services in separate terminals:**
   ```bash
   # Terminal 1 - Service A
   SERVICE_NAME=service-a SERVER_PORT=8080 java -jar target/java-tracing-sample-1.0.0.jar
   
   # Terminal 2 - Service B
   SERVICE_NAME=service-b SERVER_PORT=8081 java -jar target/java-tracing-sample-1.0.0.jar
   
   # Terminal 3 - Service C
   SERVICE_NAME=service-c SERVER_PORT=8082 java -jar target/java-tracing-sample-1.0.0.jar
   ```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVICE_NAME` | `service-a` | Service identifier (service-a, service-b, service-c) |
| `SERVER_PORT` | `8080` | Port for the service |
| `SERVICE_A_PORT` | `8080` | Port for Service A |
| `SERVICE_B_PORT` | `8081` | Port for Service B |
| `SERVICE_C_PORT` | `8082` | Port for Service C |
| `OTEL_EXPORTER_OTLP_ENDPOINT` | `http://localhost:4317` | OTLP collector endpoint |
| `OTEL_SERVICE_NAME` | `java-tracing-app` | Service name for traces |

## Coralogix Integration

To send traces to Coralogix:

1. **Update `otel-collector-config.yaml`:**
   ```yaml
   exporters:
     otlp/coralogix:
       endpoint: "https://ingress.coralogix.com:443"
       headers:
         Authorization: "Bearer YOUR_CORALOGIX_PRIVATE_KEY"
   
   service:
     pipelines:
       traces:
         exporters: [jaeger, otlp/coralogix, logging]
   ```

2. **Replace `YOUR_CORALOGIX_PRIVATE_KEY` with your actual private key**

3. **Restart the collector:**
   ```bash
   docker-compose restart otel-collector
   ```

## API Endpoints

### Service A (Main API)
- `GET /api/products` - Retrieve products (calls Service B)
- `GET /health` - Health check

### Service B (Categories)
- `GET /api/categories` - Get product categories (calls Service C)
- `GET /health` - Health check

### Service C (Tags)
- `GET /api/tags` - Get product tags
- `GET /health` - Health check

## Trace and Log Correlation

Each log entry includes:
```json
{
  "timestamp": "2024-01-15T10:30:00.123Z",
  "level": "INFO",
  "message": "Processing product request",
  "service": "java-service-a",
  "trace_id": "1234567890abcdef1234567890abcdef",
  "span_id": "1234567890abcdef"
}
```

## Testing Different Scenarios

The application randomly generates:
- **Success (80%)**: HTTP 200 responses
- **Client Error (10%)**: HTTP 400 responses  
- **Server Error (10%)**: HTTP 500 responses
- **Variable Latency**: 100-2000ms per request

Run multiple requests to see different trace patterns:
```bash
for i in {1..20}; do curl http://localhost:8080/api/products; echo; done
```

## Development

- **Run with development profile:**
  ```bash
  mvn spring-boot:run -Dspring-boot.run.profiles=dev
  ```

- **Add new dependencies:**
  ```bash
  # Edit pom.xml and run
  mvn dependency:resolve
  ```

- **Run tests:**
  ```bash
  mvn test
  ```

## Troubleshooting

1. **Services not connecting**: Ensure all services are running and ports are available
2. **No traces in collector**: Check OTEL_EXPORTER_OTLP_ENDPOINT configuration
3. **Missing trace correlation**: Verify OpenTelemetry Spring Boot starter is properly configured
4. **Build issues**: Ensure Java 17+ and Maven 3.6+ are installed
5. **Docker issues**: Run `docker-compose down -v` and `docker-compose up --build`

## Project Structure

```
java/
├── src/main/java/com/example/
│   └── Application.java          # Main Spring Boot application
├── src/main/resources/
│   ├── application.yml           # Application configuration
│   └── logback-spring.xml        # Logging configuration
├── pom.xml                       # Maven dependencies
├── docker-compose.yml            # Docker services
├── otel-collector-config.yaml    # OpenTelemetry collector config
├── Dockerfile                    # Container image
└── README.md                     # This file
```
