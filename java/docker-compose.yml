version: '3.8'

services:
  # OpenTelemetry Collector
  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "8889:8889"   # Prometheus metrics
    depends_on:
      - jaeger

  # Jaeger for trace visualization (optional)
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "14250:14250"  # Jaeger gRPC
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # Java Service A
  service-a:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SERVICE_NAME=service-a
      - SERVER_PORT=8080
      - SERVICE_A_PORT=8080
      - SERVICE_B_PORT=8081
      - SERVICE_C_PORT=8082
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317
      - OTEL_SERVICE_NAME=java-service-a
    depends_on:
      - otel-collector
      - service-b
      - service-c

  # Java Service B
  service-b:
    build: .
    ports:
      - "8081:8081"
    environment:
      - SERVICE_NAME=service-b
      - SERVER_PORT=8081
      - SERVICE_A_PORT=8080
      - SERVICE_B_PORT=8081
      - SERVICE_C_PORT=8082
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317
      - OTEL_SERVICE_NAME=java-service-b
    depends_on:
      - otel-collector
      - service-c

  # Java Service C
  service-c:
    build: .
    ports:
      - "8082:8082"
    environment:
      - SERVICE_NAME=service-c
      - SERVER_PORT=8082
      - SERVICE_A_PORT=8080
      - SERVICE_B_PORT=8081
      - SERVICE_C_PORT=8082
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317
      - OTEL_SERVICE_NAME=java-service-c
    depends_on:
      - otel-collector
