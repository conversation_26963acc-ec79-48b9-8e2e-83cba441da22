package com.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@SpringBootApplication
@RestController
public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);
    private final Tracer tracer;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    @Value("${service.name:service-a}")
    private String serviceName;

    @Value("${service.a.port:8080}")
    private int serviceAPort;

    @Value("${service.b.port:8081}")
    private int serviceBPort;

    @Value("${service.c.port:8082}")
    private int serviceCPort;

    @Autowired
    public Application(OpenTelemetry openTelemetry) {
        this.tracer = openTelemetry.getTracer("java-tracing-app");
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    // Utility methods
    private void simulateLatency() {
        try {
            int delay = ThreadLocalRandom.current().nextInt(100, 2000);
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private int simulateError() {
        double errorChance = ThreadLocalRandom.current().nextDouble();
        if (errorChance < 0.1) return 500; // 10% chance of 500 error
        if (errorChance < 0.2) return 400; // 10% chance of 400 error
        return 200;
    }

    private void addTraceToMDC() {
        Span currentSpan = Span.current();
        if (currentSpan != null) {
            String traceId = currentSpan.getSpanContext().getTraceId();
            String spanId = currentSpan.getSpanContext().getSpanId();
            MDC.put("trace_id", traceId);
            MDC.put("span_id", spanId);
            MDC.put("service", serviceName);
        }
    }

    // Service A Routes (Main API)
    @GetMapping("/api/products")
    public ResponseEntity<?> getProducts() {
        Span span = tracer.spanBuilder("get_products")
                .setSpanKind(SpanKind.SERVER)
                .startSpan();

        try (Scope scope = span.makeCurrent()) {
            addTraceToMDC();
            logger.info("Processing product request");

            simulateLatency();
            int statusCode = simulateError();

            span.setAttribute("http.method", "GET");
            span.setAttribute("http.route", "/api/products");
            span.setAttribute("http.status_code", statusCode);

            if (statusCode == 500) {
                logger.error("Internal server error occurred");
                span.setStatus(StatusCode.ERROR, "Internal server error");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Internal server error"));
            } else if (statusCode == 400) {
                logger.warn("Bad request received");
                span.setStatus(StatusCode.ERROR, "Bad request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "Bad request"));
            }

            // Call Service B
            try {
                String categoryUrl = String.format("http://localhost:%d/api/categories", serviceBPort);
                Map<String, Object> categoryData = webClient.get()
                        .uri(categoryUrl)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .block();

                List<Map<String, Object>> products = Arrays.asList(
                        Map.of("id", 1, "name", "Smartphone", "price", 699.99, "category", "Electronics"),
                        Map.of("id", 2, "name", "Headphones", "price", 199.99, "category", "Electronics"),
                        Map.of("id", 3, "name", "Book", "price", 29.99, "category", "Books")
                );

                Map<String, Object> result = Map.of(
                        "products", products,
                        "categories", categoryData,
                        "total_count", products.size(),
                        "timestamp", Instant.now().toString()
                );

                logger.info("Successfully processed {} products", products.size());
                span.setStatus(StatusCode.OK);
                return ResponseEntity.ok(result);

            } catch (Exception e) {
                logger.error("Error calling category service: {}", e.getMessage());
                span.setStatus(StatusCode.ERROR, e.getMessage());
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                        .body(Map.of("error", "Service unavailable"));
            }

        } finally {
            span.end();
            MDC.clear();
        }
    }

    // Service B Routes (Secondary API)
    @GetMapping("/api/categories")
    public ResponseEntity<?> getCategories() {
        Span span = tracer.spanBuilder("get_categories")
                .setSpanKind(SpanKind.SERVER)
                .startSpan();

        try (Scope scope = span.makeCurrent()) {
            addTraceToMDC();
            logger.info("Processing category request");

            simulateLatency();
            int statusCode = simulateError();

            span.setAttribute("http.method", "GET");
            span.setAttribute("http.route", "/api/categories");
            span.setAttribute("http.status_code", statusCode);

            if (statusCode == 500) {
                logger.error("Category service error");
                span.setStatus(StatusCode.ERROR, "Category service error");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Category service error"));
            } else if (statusCode == 400) {
                logger.warn("Invalid category request");
                span.setStatus(StatusCode.ERROR, "Invalid request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "Invalid request"));
            }

            // Call Service C
            try {
                String tagsUrl = String.format("http://localhost:%d/api/tags", serviceCPort);
                Map<String, Object> tagsData = webClient.get()
                        .uri(tagsUrl)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .block();

                Map<String, Object> categories = Map.of(
                        "active_categories", 15,
                        "featured_categories", 5,
                        "tags", tagsData,
                        "last_updated", Instant.now().toString()
                );

                logger.info("Successfully retrieved category data");
                span.setStatus(StatusCode.OK);
                return ResponseEntity.ok(categories);

            } catch (Exception e) {
                logger.error("Error calling tags service: {}", e.getMessage());
                span.setStatus(StatusCode.ERROR, e.getMessage());
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                        .body(Map.of("error", "Tags service unavailable"));
            }

        } finally {
            span.end();
            MDC.clear();
        }
    }

    // Service C Routes (Data Service)
    @GetMapping("/api/tags")
    public ResponseEntity<?> getTags() {
        Span span = tracer.spanBuilder("get_tags")
                .setSpanKind(SpanKind.SERVER)
                .startSpan();

        try (Scope scope = span.makeCurrent()) {
            addTraceToMDC();
            logger.info("Processing tags request");

            simulateLatency();
            int statusCode = simulateError();

            span.setAttribute("http.method", "GET");
            span.setAttribute("http.route", "/api/tags");
            span.setAttribute("http.status_code", statusCode);

            if (statusCode == 500) {
                logger.error("Tags service database error");
                span.setStatus(StatusCode.ERROR, "Database error");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Database error"));
            } else if (statusCode == 400) {
                logger.warn("Invalid tags query");
                span.setStatus(StatusCode.ERROR, "Invalid query");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", "Invalid query"));
            }

            Map<String, Object> tags = Map.of(
                    "popular_tags", Arrays.asList("electronics", "books", "clothing", "home"),
                    "trending_tags", Arrays.asList("smartphone", "laptop", "headphones"),
                    "total_tags", 150,
                    "active_tags", 120
            );

            logger.info("Successfully retrieved tags data");
            span.setStatus(StatusCode.OK);
            return ResponseEntity.ok(tags);

        } finally {
            span.end();
            MDC.clear();
        }
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        return ResponseEntity.ok(Map.of(
                "status", "healthy",
                "service", serviceName,
                "timestamp", Instant.now().toString()
        ));
    }
}
