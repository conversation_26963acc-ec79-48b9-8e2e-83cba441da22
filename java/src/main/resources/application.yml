server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: ${SERVICE_NAME:java-tracing-app}

# OpenTelemetry Configuration
otel:
  exporter:
    otlp:
      endpoint: ${OTEL_EXPORTER_OTLP_ENDPOINT:http://localhost:4317}
  service:
    name: ${OTEL_SERVICE_NAME:java-tracing-app}
  resource:
    attributes:
      service.name: ${OTEL_SERVICE_NAME:java-tracing-app}
      service.version: "1.0.0"

# Service Configuration
service:
  name: ${SERVICE_NAME:service-a}
  a:
    port: ${SERVICE_A_PORT:8080}
  b:
    port: ${SERVICE_B_PORT:8081}
  c:
    port: ${SERVICE_C_PORT:8082}

# Logging Configuration
logging:
  level:
    com.example: INFO
    io.opentelemetry: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{trace_id:-},%X{span_id:-}] %logger{36} - %msg%n"
