receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  memory_limiter:
    limit_mib: 512

exporters:
  # Export to <PERSON><PERSON><PERSON> for local visualization
  jaeger:
    endpoint: jaeger:14250
    tls:
      insecure: true
  
  # Export to Coralogix (replace with your actual endpoint and token)
  otlp/coralogix:
    endpoint: "https://ingress.coralogix.com:443"
    headers:
      Authorization: "Bearer YOUR_CORALOGIX_PRIVATE_KEY"
  
  # Console exporter for debugging
  logging:
    loglevel: debug

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [jaeger, logging]  # Add otlp/coralogix when ready
    
  extensions: []
