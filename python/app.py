import os
import json
import time
import random
import logging
import requests
from flask import Flask, jsonify, request
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.instrumentation.flask import <PERSON>lask<PERSON><PERSON>rumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.resources import Resource

# Configure logging with JSON format
class JSONFormatter(logging.Formatter):
    def format(self, record):
        # Get current span context
        span = trace.get_current_span()
        trace_id = format(span.get_span_context().trace_id, '032x') if span.get_span_context().trace_id != 0 else None
        span_id = format(span.get_span_context().span_id, '016x') if span.get_span_context().span_id != 0 else None
        
        log_entry = {
            'timestamp': self.formatTime(record),
            'level': record.levelname,
            'message': record.getMessage(),
            'service': os.getenv('SERVICE_NAME', 'python-tracing-app'),
            'trace_id': trace_id,
            'span_id': span_id
        }
        return json.dumps(log_entry)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(JSONFormatter())
logger.handlers = [handler]

# Initialize OpenTelemetry
resource = Resource.create({
    "service.name": os.getenv('SERVICE_NAME', 'python-tracing-app'),
    "service.version": "1.0.0",
})

trace.set_tracer_provider(TracerProvider(resource=resource))
tracer = trace.get_tracer(__name__)

# Configure OTLP exporter
otlp_exporter = OTLPSpanExporter(
    endpoint=os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317'),
    insecure=True
)

span_processor = BatchSpanProcessor(otlp_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

# Create Flask app
app = Flask(__name__)

# Instrument Flask and Requests
FlaskInstrumentor().instrument_app(app)
RequestsInstrumentor().instrument()

# Service ports
SERVICE_A_PORT = int(os.getenv('SERVICE_A_PORT', 5000))
SERVICE_B_PORT = int(os.getenv('SERVICE_B_PORT', 5001))
SERVICE_C_PORT = int(os.getenv('SERVICE_C_PORT', 5002))

def simulate_latency():
    """Simulate variable latency"""
    delay = random.uniform(0.1, 2.0)
    time.sleep(delay)
    return delay

def simulate_error():
    """Randomly simulate errors"""
    error_chance = random.random()
    if error_chance < 0.1:  # 10% chance of 500 error
        return 500
    elif error_chance < 0.2:  # 10% chance of 400 error
        return 400
    return 200

# Service A Routes (Main API)
@app.route('/api/orders', methods=['GET'])
def get_orders():
    with tracer.start_as_current_span("get_orders") as span:
        logger.info("Processing order request")
        
        latency = simulate_latency()
        status_code = simulate_error()
        
        span.set_attribute("http.method", "GET")
        span.set_attribute("http.route", "/api/orders")
        span.set_attribute("latency.seconds", latency)
        
        if status_code == 500:
            logger.error("Internal server error occurred")
            span.set_attribute("error", True)
            return jsonify({"error": "Internal server error"}), 500
        elif status_code == 400:
            logger.warning("Bad request received")
            span.set_attribute("error", True)
            return jsonify({"error": "Bad request"}), 400
        
        try:
            # Call Service B
            response = requests.get(f'http://localhost:{SERVICE_B_PORT}/api/inventory')
            inventory_data = response.json()
            
            orders = [
                {"id": 1, "product": "Laptop", "quantity": 2, "status": "pending"},
                {"id": 2, "product": "Mouse", "quantity": 5, "status": "shipped"}
            ]
            
            result = {
                "orders": orders,
                "inventory_status": inventory_data,
                "processing_time": latency
            }
            
            logger.info(f"Successfully processed {len(orders)} orders")
            return jsonify(result), 200
            
        except Exception as e:
            logger.error(f"Error calling inventory service: {str(e)}")
            span.set_attribute("error", True)
            return jsonify({"error": "Service unavailable"}), 503

# Service B Routes (Secondary API)
@app.route('/api/inventory', methods=['GET'])
def get_inventory():
    with tracer.start_as_current_span("get_inventory") as span:
        logger.info("Processing inventory request")
        
        latency = simulate_latency()
        status_code = simulate_error()
        
        span.set_attribute("http.method", "GET")
        span.set_attribute("http.route", "/api/inventory")
        span.set_attribute("latency.seconds", latency)
        
        if status_code == 500:
            logger.error("Inventory service error")
            span.set_attribute("error", True)
            return jsonify({"error": "Inventory service error"}), 500
        elif status_code == 400:
            logger.warning("Invalid inventory request")
            span.set_attribute("error", True)
            return jsonify({"error": "Invalid request"}), 400
        
        try:
            # Call Service C
            response = requests.get(f'http://localhost:{SERVICE_C_PORT}/api/stock')
            stock_data = response.json()
            
            inventory = {
                "available_items": 150,
                "low_stock_items": 5,
                "stock_details": stock_data,
                "last_updated": "2024-01-15T10:30:00Z"
            }
            
            logger.info("Successfully retrieved inventory data")
            return jsonify(inventory), 200
            
        except Exception as e:
            logger.error(f"Error calling stock service: {str(e)}")
            span.set_attribute("error", True)
            return jsonify({"error": "Stock service unavailable"}), 503

# Service C Routes (Data Service)
@app.route('/api/stock', methods=['GET'])
def get_stock():
    with tracer.start_as_current_span("get_stock") as span:
        logger.info("Processing stock request")
        
        latency = simulate_latency()
        status_code = simulate_error()
        
        span.set_attribute("http.method", "GET")
        span.set_attribute("http.route", "/api/stock")
        span.set_attribute("latency.seconds", latency)
        
        if status_code == 500:
            logger.error("Stock service database error")
            span.set_attribute("error", True)
            return jsonify({"error": "Database error"}), 500
        elif status_code == 400:
            logger.warning("Invalid stock query")
            span.set_attribute("error", True)
            return jsonify({"error": "Invalid query"}), 400
        
        stock_data = {
            "laptop": {"quantity": 25, "reserved": 5},
            "mouse": {"quantity": 100, "reserved": 10},
            "keyboard": {"quantity": 50, "reserved": 2}
        }
        
        logger.info("Successfully retrieved stock data")
        return jsonify(stock_data), 200

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "service": os.getenv('SERVICE_NAME', 'python-tracing-app')}), 200

if __name__ == '__main__':
    service_name = os.getenv('SERVICE_NAME', 'service-a')
    
    if service_name == 'service-a':
        port = SERVICE_A_PORT
    elif service_name == 'service-b':
        port = SERVICE_B_PORT
    else:
        port = SERVICE_C_PORT
    
    logger.info(f"Starting {service_name} on port {port}")
    app.run(host='0.0.0.0', port=port, debug=False)
