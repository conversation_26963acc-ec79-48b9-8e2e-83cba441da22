# Python OpenTelemetry Tracing Sample

This sample application demonstrates distributed tracing with OpenTelemetry in Python using Flask.

## Architecture

The application consists of three services:
- **Service A (Port 5000)**: Main API that handles order requests
- **Service B (Port 5001)**: Inventory service that manages stock information
- **Service C (Port 5002)**: Stock data service that provides detailed stock information

## Features

- ✅ OpenTelemetry auto-instrumentation for Flask and HTTP requests
- ✅ Distributed tracing across multiple services
- ✅ JSON structured logging with trace ID and span ID correlation
- ✅ Variable latencies (0.1-2.0 seconds)
- ✅ Multiple HTTP status codes (200, 400, 500) with 10% error rate each
- ✅ OTLP export to collector
- ✅ Ready for Coralogix APM integration

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Start all services:**
   ```bash
   docker-compose up --build
   ```

2. **Test the application:**
   ```bash
   # Generate traces with different outcomes
   curl http://localhost:5000/api/orders
   curl http://localhost:5001/api/inventory
   curl http://localhost:5002/api/stock
   ```

3. **View traces in Jaeger UI:**
   Open http://localhost:16686

### Option 2: Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start OTEL Collector:**
   ```bash
   docker run -p 4317:4317 -p 4318:4318 \
     -v $(pwd)/otel-collector-config.yaml:/etc/otel-collector-config.yaml \
     otel/opentelemetry-collector-contrib:latest \
     --config=/etc/otel-collector-config.yaml
   ```

3. **Start services in separate terminals:**
   ```bash
   # Terminal 1 - Service A
   SERVICE_NAME=service-a python app.py
   
   # Terminal 2 - Service B
   SERVICE_NAME=service-b python app.py
   
   # Terminal 3 - Service C
   SERVICE_NAME=service-c python app.py
   ```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVICE_NAME` | `service-a` | Service identifier (service-a, service-b, service-c) |
| `SERVICE_A_PORT` | `5000` | Port for Service A |
| `SERVICE_B_PORT` | `5001` | Port for Service B |
| `SERVICE_C_PORT` | `5002` | Port for Service C |
| `OTEL_EXPORTER_OTLP_ENDPOINT` | `http://localhost:4317` | OTLP collector endpoint |
| `OTEL_SERVICE_NAME` | `python-tracing-app` | Service name for traces |

## Coralogix Integration

To send traces to Coralogix:

1. **Update `otel-collector-config.yaml`:**
   ```yaml
   exporters:
     otlp/coralogix:
       endpoint: "https://ingress.coralogix.com:443"
       headers:
         Authorization: "Bearer YOUR_CORALOGIX_PRIVATE_KEY"
   
   service:
     pipelines:
       traces:
         exporters: [jaeger, otlp/coralogix, logging]
   ```

2. **Replace `YOUR_CORALOGIX_PRIVATE_KEY` with your actual private key**

3. **Restart the collector:**
   ```bash
   docker-compose restart otel-collector
   ```

## API Endpoints

### Service A (Main API)
- `GET /api/orders` - Retrieve orders (calls Service B)
- `GET /health` - Health check

### Service B (Inventory)
- `GET /api/inventory` - Get inventory status (calls Service C)
- `GET /health` - Health check

### Service C (Stock Data)
- `GET /api/stock` - Get stock details
- `GET /health` - Health check

## Trace and Log Correlation

Each log entry includes:
```json
{
  "timestamp": "2024-01-15T10:30:00.123Z",
  "level": "INFO",
  "message": "Processing order request",
  "service": "python-service-a",
  "trace_id": "1234567890abcdef1234567890abcdef",
  "span_id": "1234567890abcdef"
}
```

## Testing Different Scenarios

The application randomly generates:
- **Success (80%)**: HTTP 200 responses
- **Client Error (10%)**: HTTP 400 responses  
- **Server Error (10%)**: HTTP 500 responses
- **Variable Latency**: 0.1-2.0 seconds per request

Run multiple requests to see different trace patterns:
```bash
for i in {1..20}; do curl http://localhost:5000/api/orders; echo; done
```

## Troubleshooting

1. **Services not connecting**: Ensure all services are running and ports are available
2. **No traces in collector**: Check OTEL_EXPORTER_OTLP_ENDPOINT configuration
3. **Missing trace correlation**: Verify OpenTelemetry instrumentation is properly initialized
4. **Docker issues**: Run `docker-compose down -v` and `docker-compose up --build`
